#!/usr/bin/env python3
"""
PLANNER Node Demo Script

A simple demonstration of the PLANNER node capabilities.
This script shows how the PLANNER node can analyze a project and create implementation plans.
"""

import os
import sys
from pathlib import Path
import dotenv

# Add current directory to path for imports
sys.path.append(str(Path(__file__).parent))

dotenv.load_dotenv()

def main():
    """Run a simple PLANNER demonstration"""
    print("🚀 PLANNER Node Demonstration")
    print("=" * 50)
    
    # Check environment
    if not os.getenv("OPENROUTER_API_KEY"):
        print("❌ Error: OPENROUTER_API_KEY environment variable not set.")
        print("Please set your OpenRouter API key to run this demo.")
        print("Example: export OPENROUTER_API_KEY='your-api-key-here'")
        return 1
    
    print("✅ Environment check passed")
    print("🔄 Starting PLANNER demonstration...")
    
    # Demo query that should route to PLANNER
    demo_query = """
    I need help understanding and improving this multi-agent system project. Please:
    
    1. Explore the current project structure and identify all components
    2. Analyze the main files to understand the system architecture  
    3. Create a comprehensive improvement plan that includes:
       - Code organization suggestions
       - Documentation improvements
       - Testing strategy
       - Performance optimization opportunities
       - Security enhancements
    
    Start by examining the files and directories to get a complete picture of the system.
    """
    
    print(f"📋 Demo Query:")
    print(f"   {demo_query.strip()[:100]}...")
    print()
    
    try:
        from multi_agent_system import run_multi_agent_query_stream
        
        query_messages = [{"type": "human", "content": demo_query}]
        
        print("🎯 Processing query through multi-agent system...")
        print("-" * 50)
        
        for event in run_multi_agent_query_stream(query_messages):
            if event.get("type") == "message":
                msg = event.get("message", {})
                expert = msg.get("expert", "Unknown")
                msg_type = msg.get("type", "unknown")
                content = msg.get("content", "")
                
                if msg_type == "routing":
                    print(f"🎯 Routing Decision: {content}")
                    if "Planner" in content:
                        print("✅ Successfully routed to PLANNER node!")
                    print()
                    
                elif msg_type == "agent":
                    print(f"📋 {expert} Analysis:")
                    print("-" * 30)
                    # Show first part of response
                    lines = content.split('\n')
                    for line in lines[:10]:  # Show first 10 lines
                        print(f"   {line}")
                    if len(lines) > 10:
                        print(f"   ... ({len(lines) - 10} more lines)")
                    print()
                    
                elif msg_type == "tool_call":
                    tool_name = msg.get("tool_name", "unknown")
                    tool_args = msg.get("tool_args", {})
                    print(f"🔧 Executing Tool: {tool_name}")
                    if 'file_path' in tool_args:
                        print(f"   File: {tool_args['file_path']}")
                    elif 'directory_path' in tool_args:
                        print(f"   Directory: {tool_args['directory_path']}")
                    elif 'command' in tool_args:
                        print(f"   Command: {tool_args['command']}")
                    
                elif msg_type == "tool_result":
                    tool_name = msg.get("tool_name", "unknown")
                    print(f"✅ Tool Result ({tool_name}):")
                    # Show abbreviated result
                    result_lines = content.split('\n')
                    for line in result_lines[:5]:  # Show first 5 lines
                        print(f"   {line}")
                    if len(result_lines) > 5:
                        print(f"   ... ({len(result_lines) - 5} more lines)")
                    print()
            
            elif event.get("type") == "complete":
                expert_used = event.get("expert_used", "Unknown")
                print("=" * 50)
                print(f"✅ Demo completed successfully!")
                print(f"📊 Final expert used: {expert_used}")
                
                if expert_used == "Planner":
                    print("🎉 PLANNER node worked correctly!")
                    print("\n📋 The PLANNER node demonstrated:")
                    print("   ✓ File system exploration")
                    print("   ✓ Safe command execution")
                    print("   ✓ Comprehensive analysis")
                    print("   ✓ Structured planning")
                else:
                    print(f"⚠️  Expected PLANNER but got {expert_used}")
                
                break
                
    except ImportError as e:
        print(f"❌ Import Error: {e}")
        print("Make sure all required files are in the same directory:")
        print("- multi_agent_system.py")
        print("- planner_node.py")
        return 1
        
    except Exception as e:
        print(f"❌ Demo Error: {e}")
        return 1
    
    print("\n🎯 Demo Summary:")
    print("The PLANNER node is now integrated into the multi-agent system and can:")
    print("• Analyze complex tasks and create detailed plans")
    print("• Explore file systems safely with read_file and list_directory")
    print("• Execute safe bash commands for information gathering")
    print("• Provide structured, actionable planning recommendations")
    print("\nTry asking planning-related questions to see it in action!")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
