import { useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { ChevronDown, ChevronRight, ExternalLink, FileText, Edit, Plus, Check, X, Bug } from 'lucide-react'
import { cn } from '@/lib/utils'
import { But<PERSON> } from '@/components/ui/button'
import { Card } from '@/components/ui/card'
import { Prism as SyntaxHighlighter } from 'react-syntax-highlighter'
import { vscDarkPlus } from 'react-syntax-highlighter/dist/esm/styles/prism'

interface FileOperationEvent {
  type: 'file_operation'
  operation: 'created_file' | 'edited_file_full' | 'edited_file_diff'
  file_path: string
  success: boolean
  timestamp: string
  content?: string
  before_content?: string
  after_content?: string
  diff?: {
    diff_text: string
    added_lines: number
    removed_lines: number
    total_changes: number
  }
  prompt?: { role: string; content: string }[]
  tool_args?: Record<string, unknown>
}

interface FileOperationCardProps {
  event: FileOperationEvent
}

interface DebugModalProps {
  isOpen: boolean
  onClose: () => void
  prompt?: { role: string; content: string }[]
  tool_args?: Record<string, unknown>
}

const DebugModal = ({ isOpen, onClose, prompt, tool_args }: DebugModalProps) => {
  if (!isOpen) return null

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50" onClick={onClose}>
      <div
        className="bg-white rounded-lg p-6 max-w-4xl max-h-[80vh] overflow-y-auto m-4"
        onClick={(e) => e.stopPropagation()}
      >
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold">Debug Information</h3>
          <Button variant="ghost" size="sm" onClick={onClose}>
            <X className="w-4 h-4" />
          </Button>
        </div>

        {tool_args && (
          <div className="mb-6">
            <h4 className="text-sm font-medium mb-2">Tool Arguments</h4>
            <div className="bg-gray-100 rounded p-3 font-mono text-xs">
              <pre className="whitespace-pre-wrap">
                {JSON.stringify(tool_args, null, 2)}
              </pre>
            </div>
          </div>
        )}

        {prompt && (
          <div>
            <h4 className="text-sm font-medium mb-2">Prompt</h4>
            <div className="bg-gray-100 rounded p-3 font-mono text-xs max-h-64 overflow-y-auto">
              <pre className="whitespace-pre-wrap">
                {JSON.stringify(prompt, null, 2)}
              </pre>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}

const getOperationIcon = (operation: string) => {
  switch (operation) {
    case 'created_file':
      return <Plus className="w-4 h-4" />
    case 'edited_file_full':
      return <FileText className="w-4 h-4" />
    case 'edited_file_diff':
      return <Edit className="w-4 h-4" />
    default:
      return <FileText className="w-4 h-4" />
  }
}

const getOperationTitle = (operation: string) => {
  switch (operation) {
    case 'created_file':
      return 'Created file'
    case 'edited_file_full':
      return 'Edited file'
    case 'edited_file_diff':
      return 'Edited file'
    default:
      return 'File operation'
  }
}

const getOperationColor = (operation: string, success: boolean) => {
  if (!success) return 'text-red-600 bg-red-50 border-red-200'
  
  switch (operation) {
    case 'created_file':
      return 'text-green-600 bg-green-50 border-green-200'
    case 'edited_file_full':
      return 'text-blue-600 bg-blue-50 border-blue-200'
    case 'edited_file_diff':
      return 'text-purple-600 bg-purple-50 border-purple-200'
    default:
      return 'text-gray-600 bg-gray-50 border-gray-200'
  }
}

const DiffViewer = ({ diffText }: { diffText: string }) => {
  const lines = diffText.split('\n')
  
  return (
    <div className="font-mono text-xs bg-gray-900 text-gray-100 rounded overflow-hidden">
      {lines.map((line, index) => {
        let className = 'px-3 py-0.5'
        let prefix = ''
        
        if (line.startsWith('+++') || line.startsWith('---')) {
          className += ' bg-gray-700 text-gray-300'
        } else if (line.startsWith('@@')) {
          className += ' bg-blue-800 text-blue-200'
        } else if (line.startsWith('+')) {
          className += ' bg-green-800 text-green-200'
          prefix = '+'
        } else if (line.startsWith('-')) {
          className += ' bg-red-800 text-red-200'
          prefix = '-'
        }
        
        return (
          <div key={index} className={className}>
            <span className="select-none opacity-60 mr-2">{prefix}</span>
            {line.substring(prefix.length)}
          </div>
        )
      })}
    </div>
  )
}

export function FileOperationCard({ event }: FileOperationCardProps) {
  const [isExpanded, setIsExpanded] = useState(false)
  const [showDebug, setShowDebug] = useState(false)

  const handleOpenInNewWindow = () => {
    // This would open the file in a new window/tab
    // For now, we'll just log it
    console.log('Opening file:', event.file_path)
  }

  const colorClasses = getOperationColor(event.operation, event.success)

  return (
    <>
      <Card className={cn('border-l-4 transition-all duration-200', colorClasses)}>
        <div className="p-3">
          {/* Header */}
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <button
                onClick={() => setIsExpanded(!isExpanded)}
                className="flex items-center gap-2 hover:bg-black/5 rounded p-1 -m-1 transition-colors"
              >
                {getOperationIcon(event.operation)}
                <span className="font-medium text-sm">
                  {getOperationTitle(event.operation)}
                </span>
              </button>
            </div>

            <div className="flex items-center gap-2">
              {/* Show diff stats for edited files */}
              {event.operation !== 'created_file' && event.diff && (
                <div className="flex items-center gap-1 text-xs">
                  <span className="text-green-600">+{event.diff.added_lines}</span>
                  <span className="text-red-600">-{event.diff.removed_lines}</span>
                </div>
              )}

              {/* Expand/Collapse button */}
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setIsExpanded(!isExpanded)}
                className="h-6 w-6 p-0"
              >
                {isExpanded ? (
                  <ChevronDown className="w-3 h-3" />
                ) : (
                  <ChevronRight className="w-3 h-3" />
                )}
              </Button>

              {/* Debug button */}
              {(event.prompt || event.tool_args) && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setShowDebug(true)}
                  className="h-6 w-6 p-0"
                  title="Debug Info"
                >
                  <Bug className="w-3 h-3" />
                </Button>
              )}

              {/* Open file button */}
              <Button
                variant="ghost"
                size="sm"
                onClick={handleOpenInNewWindow}
                className="h-6 w-6 p-0"
                title="Open File"
              >
                <ExternalLink className="w-3 h-3" />
              </Button>

              {/* Success/Error indicator */}
              {event.success ? (
                <Check className="w-4 h-4 text-green-600" />
              ) : (
                <X className="w-4 h-4 text-red-600" />
              )}
            </div>
          </div>
        
        {/* File path */}
        <div className="mt-1 text-sm text-gray-600 font-mono">
          {event.file_path}
        </div>
        
        {/* Stats for diff operations */}
        {event.operation !== 'created_file' && event.diff && (
          <div className="mt-2 flex items-center gap-4 text-xs">
            {event.diff.added_lines > 0 && (
              <span className="text-green-600">
                +{event.diff.added_lines}
              </span>
            )}
            {event.diff.removed_lines > 0 && (
              <span className="text-red-600">
                -{event.diff.removed_lines}
              </span>
            )}
            <Button
              variant="ghost"
              size="sm"
              onClick={handleOpenInNewWindow}
              className="h-4 text-xs p-0 text-blue-600 hover:text-blue-800"
            >
              <ExternalLink className="w-3 h-3 mr-1" />
              Open
            </Button>
          </div>
        )}
      </div>
      
      {/* Expanded content */}
      <AnimatePresence>
        {isExpanded && (
          <motion.div
            initial={{ height: 0, opacity: 0 }}
            animate={{ height: 'auto', opacity: 1 }}
            exit={{ height: 0, opacity: 0 }}
            transition={{ duration: 0.2 }}
            className="overflow-hidden"
          >
            <div className="px-3 pb-3 border-t border-gray-200">
              {event.operation === 'created_file' && event.content && (
                <div className="mt-3">
                  <div className="text-xs text-gray-600 mb-2">File content:</div>
                  <SyntaxHighlighter
                    style={vscDarkPlus as any}
                    language="python"
                    PreTag="div"
                    className="text-xs max-h-64 overflow-y-auto"
                  >
                    {event.content}
                  </SyntaxHighlighter>
                </div>
              )}
              
              {(event.operation === 'edited_file_full' || event.operation === 'edited_file_diff') && 
               event.diff && event.diff.diff_text && (
                <div className="mt-3">
                  <div className="text-xs text-gray-600 mb-2">Changes:</div>
                  <div className="max-h-64 overflow-y-auto">
                    <DiffViewer diffText={event.diff.diff_text} />
                  </div>
                </div>
              )}
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </Card>

    <DebugModal
      isOpen={showDebug}
      onClose={() => setShowDebug(false)}
      prompt={event.prompt}
      tool_args={event.tool_args}
    />
  </>
  )
}
