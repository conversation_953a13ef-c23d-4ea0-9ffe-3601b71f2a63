import { ExternalLink, FileText, Edit, Plus } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Prism as SyntaxHighlighter } from 'react-syntax-highlighter'
import { github } from 'react-syntax-highlighter/dist/esm/styles/hljs'
import { BaseMessageCard } from './BaseMessageCard'

interface FileOperationEvent {
  type: 'file_operation'
  operation: 'created_file' | 'edited_file_full' | 'edited_file_diff'
  file_path: string
  success: boolean
  timestamp: string
  content?: string
  before_content?: string
  after_content?: string
  diff?: {
    diff_text: string
    added_lines: number
    removed_lines: number
    total_changes: number
  }
  prompt?: { role: string; content: string }[]
  tool_args?: Record<string, unknown>
}

interface FileOperationCardProps {
  event: FileOperationEvent
}

const getOperationIcon = (operation: string) => {
  switch (operation) {
    case 'created_file':
      return <Plus className="w-4 h-4" />
    case 'edited_file_full':
      return <FileText className="w-4 h-4" />
    case 'edited_file_diff':
      return <Edit className="w-4 h-4" />
    default:
      return <FileText className="w-4 h-4" />
  }
}

const getOperationTitle = (operation: string) => {
  switch (operation) {
    case 'created_file':
      return 'Created file'
    case 'edited_file_full':
      return 'Edited file'
    case 'edited_file_diff':
      return 'Edited file'
    default:
      return 'File operation'
  }
}

const getOperationColor = (operation: string, success: boolean) => {
  if (!success) return 'text-red-600 bg-red-50 border-red-200'
  
  switch (operation) {
    case 'created_file':
      return 'text-green-600 bg-green-50 border-green-200'
    case 'edited_file_full':
      return 'text-blue-600 bg-blue-50 border-blue-200'
    case 'edited_file_diff':
      return 'text-purple-600 bg-purple-50 border-purple-200'
    default:
      return 'text-gray-600 bg-gray-50 border-gray-200'
  }
}

const DiffViewer = ({ diffText }: { diffText: string }) => {
  const lines = diffText.split('\n')
  
  return (
    <div className="font-mono text-xs bg-gray-900 text-gray-100 rounded overflow-hidden">
      {lines.map((line, index) => {
        let className = 'px-3 py-0.5'
        let prefix = ''
        
        if (line.startsWith('+++') || line.startsWith('---')) {
          className += ' bg-gray-700 text-gray-300'
        } else if (line.startsWith('@@')) {
          className += ' bg-blue-800 text-blue-200'
        } else if (line.startsWith('+')) {
          className += ' bg-green-800 text-green-200'
          prefix = '+'
        } else if (line.startsWith('-')) {
          className += ' bg-red-800 text-red-200'
          prefix = '-'
        }
        
        return (
          <div key={index} className={className}>
            <span className="select-none opacity-60 mr-2">{prefix}</span>
            {line.substring(prefix.length)}
          </div>
        )
      })}
    </div>
  )
}

export function FileOperationCard({ event }: FileOperationCardProps) {
  const handleOpenInNewWindow = () => {
    // This would open the file in a new window/tab
    // For now, we'll just log it
    console.log('Opening file:', event.file_path)
  }

  const colorClasses = getOperationColor(event.operation, event.success)

  // Custom actions for file operations
  const customActions = [
    // Show diff stats for edited files
    event.operation !== 'created_file' && event.diff && (
      <div key="diff-stats" className="flex items-center gap-1 text-xs">
        <span className="text-green-600">+{event.diff.added_lines}</span>
        <span className="text-red-600">-{event.diff.removed_lines}</span>
      </div>
    ),
    // Open file button
    <Button
      key="open-file"
      variant="ghost"
      size="sm"
      onClick={handleOpenInNewWindow}
      className="h-6 w-6 p-0"
      title="Open File"
    >
      <ExternalLink className="w-3 h-3" />
    </Button>
  ].filter(Boolean)

  const expandedContent = (
    <>
      {/* File content for created files */}
      {event.operation === 'created_file' && event.content && (
        <div>
          <div className="text-xs text-gray-600 mb-2">File Content:</div>
          <SyntaxHighlighter
            language="python"
            style={github}
            customStyle={{
              margin: 0,
              borderRadius: '8px',
              fontSize: '12px',
              backgroundColor: '#f6f8fa'
            }}
          >
            {event.content}
          </SyntaxHighlighter>
        </div>
      )}

      {/* Diff view for edited files */}
      {(event.operation === 'edited_file_full' || event.operation === 'edited_file_diff') &&
       event.diff && event.diff.diff_text && (
        <div>
          <div className="text-xs text-gray-600 mb-2">Changes:</div>
          <div className="max-h-64 overflow-y-auto">
            <DiffViewer diffText={event.diff.diff_text} />
          </div>
        </div>
      )}
    </>
  )

  return (
    <BaseMessageCard
      className={`border-l-4 ${colorClasses}`}
      icon={getOperationIcon(event.operation)}
      title={getOperationTitle(event.operation)}
      subtitle={event.file_path}
      showExpandButton={true}
      showDebugButton={true}
      showSuccessIndicator={true}
      customActions={customActions}
      expandedContent={expandedContent}
      debugData={{
        prompt: event.prompt,
        tool_args: event.tool_args
      }}
      success={event.success}
    >
      <div className="text-xs text-muted-foreground font-mono">
        {event.file_path}
      </div>
    </BaseMessageCard>
  )
}
