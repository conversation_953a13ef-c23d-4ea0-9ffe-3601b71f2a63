import { useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { ChevronDown, ChevronRight, Terminal, Check, X, Bug } from 'lucide-react'
import { cn } from '@/lib/utils'
import { Button } from '@/components/ui/button'
import { Card } from '@/components/ui/card'

interface TerminalEvent {
  type: 'terminal'
  tool_name: string
  command: string
  result: string
  success: boolean
  timestamp: string
  prompt?: { role: string; content: string }[]
  tool_args?: Record<string, unknown>
}

interface TerminalCardProps {
  event: TerminalEvent
}

interface DebugModalProps {
  isOpen: boolean
  onClose: () => void
  prompt?: { role: string; content: string }[]
  tool_args?: Record<string, unknown>
}

const DebugModal = ({ isOpen, onClose, prompt, tool_args }: DebugModalProps) => {
  if (!isOpen) return null

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50" onClick={onClose}>
      <div 
        className="bg-white rounded-lg p-6 max-w-4xl max-h-[80vh] overflow-y-auto m-4"
        onClick={(e) => e.stopPropagation()}
      >
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold">Debug Information</h3>
          <Button variant="ghost" size="sm" onClick={onClose}>
            <X className="w-4 h-4" />
          </Button>
        </div>
        
        {tool_args && (
          <div className="mb-6">
            <h4 className="text-sm font-medium mb-2">Tool Arguments</h4>
            <div className="bg-gray-100 rounded p-3 font-mono text-xs">
              <pre className="whitespace-pre-wrap">
                {JSON.stringify(tool_args, null, 2)}
              </pre>
            </div>
          </div>
        )}
        
        {prompt && (
          <div>
            <h4 className="text-sm font-medium mb-2">Prompt</h4>
            <div className="bg-gray-100 rounded p-3 font-mono text-xs max-h-64 overflow-y-auto">
              <pre className="whitespace-pre-wrap">
                {JSON.stringify(prompt, null, 2)}
              </pre>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}

export function TerminalCard({ event }: TerminalCardProps) {
  const [isExpanded, setIsExpanded] = useState(false)
  const [showDebug, setShowDebug] = useState(false)
  
  const colorClasses = event.success 
    ? 'text-green-600 bg-green-50 border-green-200' 
    : 'text-red-600 bg-red-50 border-red-200'
  
  return (
    <>
      <Card className={cn('border-l-4 transition-all duration-200', colorClasses)}>
        <div className="p-3">
          {/* Header */}
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <button
                onClick={() => setIsExpanded(!isExpanded)}
                className="flex items-center gap-2 hover:bg-black/5 rounded p-1 -m-1 transition-colors"
              >
                {isExpanded ? (
                  <ChevronDown className="w-4 h-4" />
                ) : (
                  <ChevronRight className="w-4 h-4" />
                )}
                <Terminal className="w-4 h-4" />
                <span className="font-medium text-sm">
                  {event.tool_name}
                </span>
              </button>
            </div>
            
            <div className="flex items-center gap-2">
              {(event.prompt || event.tool_args) && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setShowDebug(true)}
                  className="h-6 w-6 p-0"
                >
                  <Bug className="w-3 h-3" />
                </Button>
              )}
              
              {event.success ? (
                <Check className="w-4 h-4 text-green-600" />
              ) : (
                <X className="w-4 h-4 text-red-600" />
              )}
            </div>
          </div>
          
          {/* Command */}
          <div className="mt-1 text-sm text-gray-600 font-mono bg-gray-100 rounded px-2 py-1">
            $ {event.command}
          </div>
        </div>
        
        {/* Expanded content */}
        <AnimatePresence>
          {isExpanded && (
            <motion.div
              initial={{ height: 0, opacity: 0 }}
              animate={{ height: 'auto', opacity: 1 }}
              exit={{ height: 0, opacity: 0 }}
              transition={{ duration: 0.2 }}
              className="overflow-hidden"
            >
              <div className="px-3 pb-3 border-t border-gray-200">
                <div className="mt-3">
                  <div className="text-xs text-gray-600 mb-2">Output:</div>
                  <div className="bg-gray-900 text-gray-100 rounded p-3 font-mono text-xs max-h-64 overflow-y-auto">
                    <pre className="whitespace-pre-wrap">
                      {event.result}
                    </pre>
                  </div>
                </div>
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </Card>
      
      <DebugModal 
        isOpen={showDebug}
        onClose={() => setShowDebug(false)}
        prompt={event.prompt}
        tool_args={event.tool_args}
      />
    </>
  )
}
