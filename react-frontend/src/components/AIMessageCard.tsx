import { BaseMessageCard } from './BaseMessageCard'
import { Markdown<PERSON>ender<PERSON> } from './MarkdownRenderer'

interface AIMessageCardProps {
  content: string
  expert?: string
  expert_icon?: string
  prompt?: { role: string; content: string }[]
  timestamp: Date
}

export function AIMessageCard({ 
  content, 
  expert, 
  expert_icon, 
  prompt,
  timestamp 
}: AIMessageCardProps) {
  return (
    <BaseMessageCard
      className="bg-muted"
      icon={<span className="text-base">{expert_icon}</span>}
      title={expert || 'AI Assistant'}
      showCopyButton={true}
      showDebugButton={true}
      copyContent={content}
      debugData={{ prompt }}
    >
      <div className="text-sm leading-relaxed">
        <MarkdownRenderer content={content} />
      </div>
    </BaseMessageCard>
  )
}
