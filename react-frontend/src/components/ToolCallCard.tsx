import { Cog } from 'lucide-react'
import { BaseMessageCard } from './BaseMessageCard'
import { CodeBlock } from './CodeBlock'

interface ToolCallEvent {
  type: 'tool_call'
  tool_name: string
  command: string
  result: string
  success: boolean
  timestamp: string
  prompt?: { role: string; content: string }[]
  tool_args?: Record<string, unknown>
}

interface ToolCallCardProps {
  event: ToolCallEvent
}

const getToolTitle = (event: ToolCallEvent) => {
  if (event.tool_name === 'read_file') {
    return 'Read file'
  } else if (event.tool_name === 'list_directory') {
    return 'List directory'
  } else {
    return event.tool_name
  }
}

const getMainContent = (event: ToolCallEvent) => {
  // Extract meaningful content based on tool type
  if (event.tool_name === 'list_directory') {
    // Show directory path
    const args = event.tool_args as any
    return args?.path || 'Directory listing'
  } else if (event.tool_name === 'read_file') {
    // Show file path and line info
    const args = event.tool_args as any
    const fileName = args?.path?.split('/').pop() || 'unknown file'
    // For now, assume reading entire file, but prepare for line ranges
    return `${fileName} (entire file)`
  } else {
    // For other tools, show the command or tool name
    return event.command || event.tool_name
  }
}

export function ToolCallCard({ event }: ToolCallCardProps) {
  const colorClasses = event.success
    ? 'text-blue-600 bg-blue-50 border-blue-200'
    : 'text-red-600 bg-red-50 border-red-200'

  // Get file language for syntax highlighting
  const getLanguageFromPath = (filePath: string) => {
    const ext = filePath.split('.').pop()?.toLowerCase()
    const languageMap: Record<string, string> = {
      'py': 'python',
      'js': 'javascript',
      'ts': 'typescript',
      'jsx': 'jsx',
      'tsx': 'tsx',
      'html': 'html',
      'css': 'css',
      'json': 'json',
      'md': 'markdown',
      'yml': 'yaml',
      'yaml': 'yaml',
      'xml': 'xml',
      'sh': 'bash',
      'sql': 'sql',
      'txt': 'text'
    }
    return languageMap[ext || ''] || 'text'
  }

  const expandedContent = (
    <div>
      {event.tool_name === 'read_file' ? (
        // Special handling for read_file to show syntax highlighted content
        (() => {
          const args = event.tool_args as any
          const filePath = args?.path || ''
          const fileName = filePath.split('/').pop() || 'File content'
          const language = getLanguageFromPath(filePath)

          return (
            <CodeBlock
              code={event.result}
              language={language}
              title={fileName}
              showLineNumbers={true}
              maxHeight="400px"
            />
          )
        })()
      ) : (
        // Default result display for other tools
        <div className="bg-gray-100 text-gray-800 rounded-lg p-3 font-mono text-xs max-h-64 overflow-y-auto">
          <pre className="whitespace-pre-wrap">
            {event.result}
          </pre>
        </div>
      )}
    </div>
  )
  
  return (
    <BaseMessageCard
      className={`border-l-4 ${colorClasses}`}
      icon={<Cog className="w-5 h-5 text-blue-600" />}
      title={getToolTitle(event)}
      showExpandButton={true}
      showDebugButton={true}
      showSuccessIndicator={true}
      expandedContent={expandedContent}
      debugData={{
        prompt: event.prompt,
        tool_args: event.tool_args
      }}
      success={event.success}
    >
      <div className="text-sm font-mono text-gray-700">
        {getMainContent(event)}
      </div>
    </BaseMessageCard>
  )
}
