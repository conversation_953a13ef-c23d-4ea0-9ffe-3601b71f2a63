import { Wrench } from 'lucide-react'
import { BaseMessageCard } from './BaseMessageCard'

interface ToolCallEvent {
  type: 'tool_call'
  tool_name: string
  command: string
  result: string
  success: boolean
  timestamp: string
  prompt?: { role: string; content: string }[]
  tool_args?: Record<string, unknown>
}

interface ToolCallCardProps {
  event: ToolCallEvent
}

export function ToolCallCard({ event }: ToolCallCardProps) {
  const colorClasses = event.success 
    ? 'text-blue-600 bg-blue-50 border-blue-200' 
    : 'text-red-600 bg-red-50 border-red-200'
  
  const expandedContent = (
    <div>
      <div className="text-xs text-gray-600 mb-2">Result:</div>
      <div className="bg-gray-100 text-gray-800 rounded-lg p-3 font-mono text-xs max-h-64 overflow-y-auto">
        <pre className="whitespace-pre-wrap">
          {event.result}
        </pre>
      </div>
    </div>
  )
  
  return (
    <BaseMessageCard
      className={`border-l-4 ${colorClasses}`}
      icon={<Wrench className="w-4 h-4" />}
      title={event.tool_name}
      showExpandButton={true}
      showDebugButton={true}
      showSuccessIndicator={true}
      expandedContent={expandedContent}
      debugData={{
        prompt: event.prompt,
        tool_args: event.tool_args
      }}
      success={event.success}
    >
      <div className="text-sm text-gray-600 font-mono bg-gray-100 rounded-lg px-3 py-2">
        {event.command}
      </div>
    </BaseMessageCard>
  )
}
