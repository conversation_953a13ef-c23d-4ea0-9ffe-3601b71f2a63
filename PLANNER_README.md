# PLANNER Node Implementation

## Overview

The PLANNER node is a specialized AI agent designed for comprehensive task analysis, planning, and information gathering. It extends the multi-agent system with powerful planning capabilities while maintaining strict security controls.

## Features

### 🎯 Core Capabilities
- **Task Analysis**: Break down complex tasks into manageable components
- **File System Exploration**: Read files and explore directory structures safely
- **Safe Command Execution**: Execute whitelisted read-only bash commands
- **Comprehensive Planning**: Create detailed execution plans with dependencies and timelines
- **Risk Assessment**: Identify potential issues and provide mitigation strategies

### 🔧 Available Tools

#### 1. `read_file(file_path: str)`
- Read and analyze file contents for planning purposes
- Automatically truncates large files for analysis
- Safe path validation to prevent directory traversal

#### 2. `list_directory(directory_path: str)`
- Explore directory structures and file organization
- Provides file sizes and types
- Safe directory access with sandboxing

#### 3. `execute_safe_bash(command: str, working_directory: str)`
- Execute safe, read-only bash commands
- Whitelist-controlled command execution
- Automatic timeout and output limiting

### 🔒 Security Features

#### Command Whitelist
Safe commands include:
- **File operations**: `ls`, `cat`, `head`, `tail`, `find`, `stat`
- **System info**: `ps`, `top`, `free`, `df`, `uname`, `whoami`
- **Text processing**: `grep`, `awk`, `sed`, `sort`, `wc`
- **Development tools**: `git` (read-only), `python --version`, `npm list`

#### Blocked Patterns
Dangerous operations are automatically blocked:
- File modifications: `rm`, `mv`, `cp`, `chmod`
- System control: `sudo`, `kill`, `reboot`, `shutdown`
- Code execution: `eval`, `exec`, `python -c`
- Output redirection: `>`, `>>`, `|`

## System Prompts

The PLANNER node includes four specialized system prompts:

### 1. Comprehensive Planning (Default)
- General-purpose planning for any type of task
- Structured methodology with discovery, analysis, and planning phases
- Suitable for most planning scenarios

### 2. Technical Planning
- Specialized for software development and system administration
- Focus on architecture, implementation, testing, and deployment
- Includes technical best practices and industry standards

### 3. Research Planning
- Optimized for information gathering and analysis tasks
- Systematic research methodology and documentation planning
- Emphasis on verification and cross-referencing

### 4. Project Planning
- Project management and coordination focus
- Scope definition, resource allocation, and timeline planning
- Risk management and stakeholder communication

## Usage Examples

### Basic Usage
```python
from multi_agent_system import run_multi_agent_query_stream

# Simple planning query
query = "Analyze this project structure and create an improvement plan"
messages = [{"type": "human", "content": query}]

for event in run_multi_agent_query_stream(messages):
    # Process events...
```

### Direct Tool Usage
```python
from planner_node import read_file, list_directory, execute_safe_bash

# Read a file
content = read_file.invoke({"file_path": "config.py"})

# List directory
files = list_directory.invoke({"directory_path": "src/"})

# Execute safe command
result = execute_safe_bash.invoke({"command": "git status"})
```

## Integration with Multi-Agent System

The PLANNER node is fully integrated into the existing multi-agent workflow:

1. **Routing**: The Coordinator automatically routes planning-related queries to the PLANNER
2. **Tool Execution**: PLANNER tools are available in the tool executor
3. **State Management**: Maintains state consistency with other agents
4. **Streaming**: Supports real-time streaming of planning results

### Routing Examples
Queries that typically route to PLANNER:
- "Create a plan for this project"
- "Analyze the current system and suggest improvements"
- "Break down this complex task into steps"
- "What files are in this directory and what do they do?"

## Testing

### Run Tests
```bash
python test_planner_node.py
```

### Run Examples
```bash
python planner_examples.py
```

## Configuration

### Environment Variables
- `OPENROUTER_API_KEY`: Required for LLM access
- `LLM_MODEL`: Model to use (default: "openai/gpt-4o")

### Customization
- Modify `SAFE_BASH_COMMANDS` to adjust command whitelist
- Update `DANGEROUS_PATTERNS` to add security restrictions
- Customize system prompts in `PLANNER_SYSTEM_PROMPTS`

## File Structure

```
playground/01-langgraph-basics/
├── planner_node.py           # Core PLANNER implementation
├── multi_agent_system.py     # Updated with PLANNER integration
├── test_planner_node.py      # Test suite
├── planner_examples.py       # Usage examples
└── PLANNER_README.md         # This documentation
```

## Security Considerations

1. **Sandboxing**: All file operations are restricted to safe directories
2. **Command Filtering**: Strict whitelist prevents dangerous operations
3. **Timeout Protection**: Commands have automatic timeout limits
4. **Output Limiting**: Large outputs are truncated to prevent memory issues
5. **Path Validation**: Prevents directory traversal attacks

## Best Practices

1. **Start with Exploration**: Always begin by exploring the current environment
2. **Create Structured Plans**: Use clear milestones and success criteria
3. **Consider Dependencies**: Plan tasks in logical order with dependencies
4. **Include Risk Assessment**: Identify potential issues and mitigation strategies
5. **Provide Alternatives**: Include fallback options when possible

## Troubleshooting

### Common Issues

1. **Import Errors**: Ensure `planner_node.py` is in the same directory
2. **Permission Denied**: Check file permissions and directory access
3. **Command Rejected**: Verify command is in the whitelist
4. **API Errors**: Ensure `OPENROUTER_API_KEY` is set correctly

### Debug Mode
Enable detailed logging:
```python
import logging
logging.basicConfig(level=logging.DEBUG)
```

## Contributing

To extend the PLANNER node:

1. **Add New Tools**: Implement new `@tool` decorated functions
2. **Update Whitelist**: Add safe commands to `SAFE_BASH_COMMANDS`
3. **Create Prompts**: Add specialized system prompts for new domains
4. **Add Tests**: Include tests for new functionality

## License

This implementation is part of the multi-agent system project and follows the same licensing terms.
