#!/usr/bin/env python3
"""
PLANNER Node Examples / PLANNER节点示例

Simple examples demonstrating PLANNER node capabilities.
演示PLANNER节点功能的简单示例。
"""

import os
import sys
from pathlib import Path
import dotenv

dotenv.load_dotenv()
sys.path.append(str(Path(__file__).parent))

def example_project_analysis():
    """项目分析示例 / Project Analysis Example"""
    return """
    Analyze the test_sandbox project and create an improvement plan:
    分析test_sandbox项目并创建改进计划：

    I need you to first copy the test_sandbox directory to the current working directory so we can analyze it.
    我需要你首先将test_sandbox目录复制到当前工作目录，这样我们就可以分析它了。

    Please execute this command first:
    请先执行这个命令：
    cp -r ../test_sandbox ./test_sandbox

    Then:
    然后：
    1. Explore the test_sandbox directory structure / 探索test_sandbox目录结构
    2. Read key files like README.md, requirements.txt, and source code / 读取关键文件如README.md、requirements.txt和源代码
    3. Identify main components and architecture / 识别主要组件和架构
    4. Suggest improvements for code organization, testing, and documentation / 建议代码组织、测试和文档的改进

    Please start by copying the test_sandbox directory and then listing its contents.
    请从复制test_sandbox目录开始，然后列出其内容。
    """

def example_system_exploration():
    """系统探索示例 / System Exploration Example"""
    return """
    Help me understand the test_sandbox codebase:
    帮我理解test_sandbox代码库：

    First, please copy the test_sandbox directory to the current working directory:
    首先，请将test_sandbox目录复制到当前工作目录：
    cp -r ../test_sandbox ./test_sandbox

    Then analyze the project:
    然后分析项目：
    1. List all files and directories in test_sandbox / 列出test_sandbox中的所有文件和目录
    2. Read configuration files like config.yaml and requirements.txt / 读取配置文件如config.yaml和requirements.txt
    3. Analyze the main source code in src/ directory / 分析src/目录中的主要源代码
    4. Examine the test structure in tests/ / 检查tests/中的测试结构
    5. Create a comprehensive project overview / 创建全面的项目概述

    Please start by copying and then exploring the test_sandbox directory structure.
    请从复制然后探索test_sandbox目录结构开始。
    """

def example_feature_planning():
    """功能规划示例 / Feature Planning Example"""
    return """
    Plan adding a new API module to the test_sandbox project:
    规划为test_sandbox项目添加新的API模块：

    First, copy the test_sandbox directory to the current working directory:
    首先，将test_sandbox目录复制到当前工作目录：
    cp -r ../test_sandbox ./test_sandbox

    Then plan the new feature:
    然后规划新功能：
    1. Analyze the current project structure in test_sandbox / 分析test_sandbox中的当前项目结构
    2. Examine existing models and utilities in src/ / 检查src/中现有的模型和工具
    3. Design a new REST API module that integrates with existing code / 设计与现有代码集成的新REST API模块
    4. Plan the API endpoints, request/response models / 规划API端点、请求/响应模型
    5. Create testing strategy for the new API / 为新API创建测试策略
    6. Update documentation to include API usage / 更新文档以包含API使用说明

    Please start by copying the test_sandbox directory and then examining the project structure.
    请从复制test_sandbox目录开始，然后检查项目结构。
    """



def run_example(query, name):
    """运行示例 / Run Example"""
    print(f"\n🚀 {name}")
    print("=" * 50)
    print(f"Query / 查询: {query.strip()[:100]}...")
    print("-" * 50)

    try:
        from multi_agent_system import run_multi_agent_query_stream

        messages = [{"type": "human", "content": query}]

        for event in run_multi_agent_query_stream(messages):
            if event.get("type") == "message":
                msg = event.get("message", {})
                msg_type = msg.get("type", "unknown")
                content = msg.get("content", "")

                if msg_type == "routing":
                    print(f"🎯 Routing: {content}")
                elif msg_type == "agent":
                    expert = msg.get("expert", "Unknown")
                    print(f"\n📋 {expert}:")
                    # Show first 300 chars
                    print(content[:300] + "..." if len(content) > 300 else content)
                elif msg_type == "tool_call":
                    tool_name = msg.get("tool_name", "unknown")
                    print(f"\n🔧 Tool: {tool_name}")
                elif msg_type == "tool_result":
                    print(f"✅ Result: {content[:200]}..." if len(content) > 200 else content)

            elif event.get("type") == "complete":
                expert = event.get("expert_used", "Unknown")
                print(f"\n✅ Completed by: {expert}")
                break

    except Exception as e:
        print(f"❌ Error: {e}")

def main():
    """主函数 / Main Function"""
    print("🎯 PLANNER Node Examples / PLANNER节点示例")
    print("=" * 50)

    if not os.getenv("OPENROUTER_API_KEY"):
        print("⚠️  OPENROUTER_API_KEY not set / 未设置API密钥")
        return 1

    examples = [
        ("Project Analysis / 项目分析", example_project_analysis),
        ("System Exploration / 系统探索", example_system_exploration),
        ("Feature Planning / 功能规划", example_feature_planning),
    ]

    print("Available examples / 可用示例:")
    for i, (name, _) in enumerate(examples, 1):
        print(f"{i}. {name}")

    print("\nSelect (1-3) or 'all' / 选择(1-3)或'all':")
    try:
        choice = input("> ").strip().lower()

        if choice == 'all':
            for name, func in examples:
                query = func()
                run_example(query, name)
                print("\n" + "="*60 + "\n")
        elif choice.isdigit() and 1 <= int(choice) <= len(examples):
            idx = int(choice) - 1
            name, func = examples[idx]
            query = func()
            run_example(query, name)
        else:
            print("Invalid choice / 无效选择")
            return 1

    except KeyboardInterrupt:
        print("\nInterrupted / 已中断")
        return 0
    except Exception as e:
        print(f"Error / 错误: {e}")
        return 1

    print("\n🎉 Examples completed / 示例完成!")
    return 0

if __name__ == "__main__":
    sys.exit(main())
