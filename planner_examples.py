#!/usr/bin/env python3
"""
PLANNER Node Usage Examples

This file demonstrates various use cases for the PLANNER node including:
1. Project analysis and planning
2. System exploration and documentation
3. Task breakdown and execution planning
4. Technical planning scenarios
"""

import os
import sys
from pathlib import Path
import dotenv

dotenv.load_dotenv()

# Add current directory to path for imports
sys.path.append(str(Path(__file__).parent))

def example_project_analysis():
    """Example: Analyze a project and create an implementation plan"""
    print("📋 Example 1: Project Analysis and Planning")
    print("=" * 50)
    
    query = """
    I have a Python project that needs to be refactored and improved. 
    Please analyze the current project structure, identify the main components, 
    and create a detailed plan for:
    1. Code organization and structure improvements
    2. Adding proper documentation
    3. Implementing testing
    4. Setting up CI/CD pipeline
    
    Start by exploring the current directory structure and understanding what we have.
    """
    
    return query

def example_system_exploration():
    """Example: Explore and document a system"""
    print("📋 Example 2: System Exploration and Documentation")
    print("=" * 50)
    
    query = """
    I need to understand this codebase better. Please:
    1. Explore the directory structure and identify all major components
    2. Read key configuration files and understand the project setup
    3. Analyze the main code files to understand the architecture
    4. Create a comprehensive documentation plan that covers:
       - Project overview and purpose
       - Architecture and component relationships
       - Setup and installation instructions
       - Usage examples and API documentation
    
    Please start by examining the files and directories to get a complete picture.
    """
    
    return query

def example_development_environment():
    """Example: Plan development environment setup"""
    print("📋 Example 3: Development Environment Setup Planning")
    print("=" * 50)
    
    query = """
    I need to set up a development environment for this project. Please create a detailed plan that includes:
    
    1. System requirements analysis (check current system capabilities)
    2. Dependency management strategy
    3. Development tools and IDE setup
    4. Version control configuration
    5. Testing environment setup
    6. Documentation and code quality tools
    
    Start by analyzing the current system and project requirements, then provide step-by-step instructions.
    """
    
    return query

def example_feature_implementation():
    """Example: Plan new feature implementation"""
    print("📋 Example 4: Feature Implementation Planning")
    print("=" * 50)
    
    query = """
    I want to add a new feature to this multi-agent system: a MEMORY node that can store and retrieve 
    information across conversations. Please create a comprehensive implementation plan that includes:
    
    1. Analysis of the current system architecture
    2. Design of the MEMORY node integration points
    3. Data storage and retrieval strategy
    4. API design for memory operations
    5. Testing strategy for the new feature
    6. Documentation requirements
    7. Migration plan for existing data
    
    Please start by examining the current codebase to understand how nodes are implemented and integrated.
    """
    
    return query

def example_performance_optimization():
    """Example: Performance analysis and optimization planning"""
    print("📋 Example 5: Performance Optimization Planning")
    print("=" * 50)
    
    query = """
    This system might have performance bottlenecks. Please create an optimization plan:
    
    1. Analyze the current system architecture for potential bottlenecks
    2. Identify resource-intensive operations
    3. Plan performance monitoring and profiling strategy
    4. Design optimization approaches for:
       - Memory usage
       - Processing speed
       - Network operations
       - File I/O operations
    5. Create a testing plan to measure improvements
    6. Plan gradual rollout of optimizations
    
    Start by examining the code to understand the current implementation and identify areas for improvement.
    """
    
    return query

def example_security_assessment():
    """Example: Security assessment and improvement planning"""
    print("📋 Example 6: Security Assessment Planning")
    print("=" * 50)
    
    query = """
    I need to assess and improve the security of this system. Please create a security improvement plan:
    
    1. Analyze current security measures in the codebase
    2. Identify potential security vulnerabilities
    3. Review file access and command execution security
    4. Plan input validation and sanitization improvements
    5. Design authentication and authorization strategy
    6. Plan security testing and monitoring
    7. Create incident response procedures
    
    Please start by examining the current security implementations, especially around file access and command execution.
    """
    
    return query

def run_planner_example(query, example_name):
    """Run a PLANNER example with the multi-agent system"""
    print(f"\n🚀 Running {example_name}")
    print("=" * 60)
    print(f"Query: {query[:200]}...")
    print("-" * 60)
    
    try:
        from multi_agent_system import run_multi_agent_query_stream
        
        query_messages = [{"type": "human", "content": query}]
        
        for event in run_multi_agent_query_stream(query_messages):
            if event.get("type") == "message":
                msg = event.get("message", {})
                expert = msg.get("expert", "Unknown")
                msg_type = msg.get("type", "unknown")
                content = msg.get("content", "")
                
                if msg_type == "routing":
                    print(f"🎯 {content}")
                elif msg_type == "agent":
                    print(f"\n📋 {expert} Response:")
                    print(content)
                elif msg_type == "tool_call":
                    tool_name = msg.get("tool_name", "unknown")
                    tool_args = msg.get("tool_args", {})
                    print(f"\n🔧 Executing: {tool_name}")
                    if tool_args:
                        print(f"   Args: {tool_args}")
                elif msg_type == "tool_result":
                    tool_name = msg.get("tool_name", "unknown")
                    print(f"\n✅ {tool_name} Result:")
                    print(content[:500] + "..." if len(content) > 500 else content)
            
            elif event.get("type") == "complete":
                expert_used = event.get("expert_used", "Unknown")
                print(f"\n✅ Task completed by: {expert_used}")
                
    except Exception as e:
        print(f"❌ Error running example: {e}")

def main():
    """Run PLANNER examples"""
    print("🎯 PLANNER Node Usage Examples")
    print("=" * 60)
    
    # Check if API key is available
    if not os.getenv("OPENROUTER_API_KEY"):
        print("⚠️  Warning: OPENROUTER_API_KEY not set. Examples will not work.")
        print("Please set your OpenRouter API key to run these examples.")
        return 1
    
    examples = [
        ("Project Analysis", example_project_analysis),
        ("System Exploration", example_system_exploration),
        ("Development Environment Setup", example_development_environment),
        ("Feature Implementation", example_feature_implementation),
        ("Performance Optimization", example_performance_optimization),
        ("Security Assessment", example_security_assessment),
    ]
    
    print("Available examples:")
    for i, (name, _) in enumerate(examples, 1):
        print(f"{i}. {name}")
    
    print("\nSelect an example to run (1-6), or 'all' to run all examples:")
    try:
        choice = input("> ").strip().lower()
        
        if choice == 'all':
            for name, example_func in examples:
                query = example_func()
                run_planner_example(query, name)
                print("\n" + "="*80 + "\n")
        elif choice.isdigit() and 1 <= int(choice) <= len(examples):
            idx = int(choice) - 1
            name, example_func = examples[idx]
            query = example_func()
            run_planner_example(query, name)
        else:
            print("Invalid choice. Please select 1-6 or 'all'.")
            return 1
            
    except KeyboardInterrupt:
        print("\n\nExample execution interrupted.")
        return 0
    except Exception as e:
        print(f"Error running examples: {e}")
        return 1
    
    print("\n🎉 Examples completed!")
    return 0

if __name__ == "__main__":
    sys.exit(main())
