#!/usr/bin/env python3
"""
PLANNER Node Examples

Simple examples demonstrating PLANNER node capabilities.
"""

import os
import sys
from pathlib import Path
import dotenv

dotenv.load_dotenv()
sys.path.append(str(Path(__file__).parent))

def example_project_analysis():
    """Project Analysis Example"""
    return """
    Analyze the test_sandbox project and create an improvement plan:

    The test_sandbox directory has been prepared in your working directory.

    Please:
    1. Explore the test_sandbox directory structure
    2. Read key files like README.md, requirements.txt, and source code
    3. Identify main components and architecture
    4. Suggest improvements for code organization, testing, and documentation

    Please start by listing the contents of the test_sandbox directory.
    """

def example_system_exploration():
    """System Exploration Example"""
    return """
    Help me understand the test_sandbox codebase:

    The test_sandbox directory is ready in your working directory.

    Please analyze the project:
    1. List all files and directories in test_sandbox
    2. Read configuration files like config.yaml and requirements.txt
    3. Analyze the main source code in src/ directory
    4. Examine the test structure in tests/
    5. Create a comprehensive project overview

    Please start by exploring the test_sandbox directory structure.
    """

def example_feature_planning():
    """Feature Planning Example"""
    return """
    Plan adding a new API module to the test_sandbox project:

    The test_sandbox directory is available in your working directory.

    Please plan the new feature:
    1. Analyze the current project structure in test_sandbox
    2. Examine existing models and utilities in src/
    3. Design a new REST API module that integrates with existing code
    4. Plan the API endpoints, request/response models
    5. Create testing strategy for the new API
    6. Update documentation to include API usage

    Please start by examining the test_sandbox project structure.
    """



def setup_test_environment():
    """Setup Test Environment"""
    import os
    import shutil
    from pathlib import Path

    # Get current script directory
    current_dir = Path(__file__).parent
    test_sandbox_src = current_dir / "test_sandbox"

    # Find the latest output directory
    output_base = current_dir / "output"
    if not output_base.exists():
        print("⚠️  No output directory found. Please run the system first.")
        return None

    # Get the most recent output directory
    output_dirs = [d for d in output_base.iterdir() if d.is_dir()]
    if not output_dirs:
        print("⚠️  No session directories found in output.")
        return None

    latest_output = max(output_dirs, key=lambda x: x.stat().st_mtime)
    test_sandbox_dest = latest_output / "test_sandbox"

    # Copy test_sandbox to the latest output directory
    if test_sandbox_src.exists():
        if test_sandbox_dest.exists():
            shutil.rmtree(test_sandbox_dest)
        shutil.copytree(test_sandbox_src, test_sandbox_dest)
        print(f"📁 Copied test_sandbox to: {test_sandbox_dest}")
        return str(test_sandbox_dest)
    else:
        print("❌ test_sandbox directory not found!")
        return None

def run_example(query, name):
    """Run Example"""
    print(f"\n🚀 {name}")
    print("=" * 50)

    # Setup test environment first
    test_sandbox_path = setup_test_environment()
    if not test_sandbox_path:
        print("❌ Failed to setup test environment")
        return

    print(f"Query: {query.strip()[:100]}...")
    print("-" * 50)

    try:
        from multi_agent_system import run_multi_agent_query_stream

        messages = [{"type": "human", "content": query}]

        for event in run_multi_agent_query_stream(messages):
            if event.get("type") == "message":
                msg = event.get("message", {})
                msg_type = msg.get("type", "unknown")
                content = msg.get("content", "")

                if msg_type == "routing":
                    print(f"🎯 Routing: {content}")
                elif msg_type == "agent":
                    expert = msg.get("expert", "Unknown")
                    print(f"\n📋 {expert}:")
                    # Show first 300 chars
                    print(content[:300] + "..." if len(content) > 300 else content)
                elif msg_type == "tool_call":
                    tool_name = msg.get("tool_name", "unknown")
                    print(f"\n🔧 Tool: {tool_name}")
                elif msg_type == "tool_result":
                    print(f"✅ Result: {content[:200]}..." if len(content) > 200 else content)

            elif event.get("type") == "complete":
                expert = event.get("expert_used", "Unknown")
                print(f"\n✅ Completed by: {expert}")
                break

    except Exception as e:
        print(f"❌ Error: {e}")

def main():
    """Main Function"""
    print("🎯 PLANNER Node Examples")
    print("=" * 50)

    if not os.getenv("OPENROUTER_API_KEY"):
        print("⚠️  OPENROUTER_API_KEY not set")
        return 1

    examples = [
        ("Project Analysis", example_project_analysis),
        ("System Exploration", example_system_exploration),
        ("Feature Planning", example_feature_planning),
    ]

    print("Available examples:")
    for i, (name, _) in enumerate(examples, 1):
        print(f"{i}. {name}")

    print("\nSelect (1-3) or 'all':")
    try:
        choice = input("> ").strip().lower()

        if choice == 'all':
            for name, func in examples:
                query = func()
                run_example(query, name)
                print("\n" + "="*60 + "\n")
        elif choice.isdigit() and 1 <= int(choice) <= len(examples):
            idx = int(choice) - 1
            name, func = examples[idx]
            query = func()
            run_example(query, name)
        else:
            print("Invalid choice")
            return 1

    except KeyboardInterrupt:
        print("\nInterrupted")
        return 0
    except Exception as e:
        print(f"Error: {e}")
        return 1

    print("\n🎉 Examples completed!")
    return 0

if __name__ == "__main__":
    sys.exit(main())
