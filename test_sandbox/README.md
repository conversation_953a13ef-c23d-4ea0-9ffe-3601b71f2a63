# Sample Project for PLANNER Testing

This is a sample project structure used for testing the PLANNER node capabilities.

## Project Structure

```
test_sandbox/
├── README.md              # This file
├── requirements.txt       # Python dependencies
├── config.yaml           # Configuration file
├── src/                   # Source code
│   ├── __init__.py
│   ├── main.py           # Main application
│   ├── models.py         # Data models
│   └── utils.py          # Utility functions
├── tests/                # Test files
│   ├── __init__.py
│   ├── test_main.py      # Main tests
│   └── test_utils.py     # Utility tests
├── docs/                 # Documentation
│   ├── api.md           # API documentation
│   └── setup.md         # Setup instructions
└── scripts/             # Build/deployment scripts
    └── deploy.sh        # Deployment script
```

## Features

- Multi-module Python application
- Comprehensive test suite
- Documentation
- Configuration management
- Deployment scripts

## Purpose

This structure provides a realistic project for the PLANNER node to analyze and create improvement plans for.
