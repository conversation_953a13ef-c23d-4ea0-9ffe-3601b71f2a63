# PLANNER Node Guide

## Overview

The PLANNER node is a specialized AI agent for comprehensive task analysis, planning, and safe information gathering within the multi-agent system.

## Key Features

### 🔧 Tools
- **`read_file`**: Safe file reading
- **`list_directory`**: Directory exploration
- **`execute_safe_bash`**: Whitelisted command execution

### 🔒 Security
- **Command whitelist**: 60+ safe read-only commands
- **Path validation**: Prevents directory traversal
- **Timeout protection**: 30-second limit
- **Output limiting**: Prevents memory overflow

## Quick Start

### Environment Setup
```bash
export OPENROUTER_API_KEY="your-api-key"
```

### Basic Usage
```python
from multi_agent_system import run_multi_agent_query_stream

query = "Analyze this project structure and create an improvement plan"

messages = [{"type": "human", "content": query}]
for event in run_multi_agent_query_stream(messages):
    # Process events
    pass
```

### Direct Tool Usage
```python
from planner_node import read_file, list_directory, execute_safe_bash

# Read file
content = read_file.invoke({"file_path": "config.py"})

# List directory
files = list_directory.invoke({"directory_path": "src/"})

# Execute safe command
result = execute_safe_bash.invoke({"command": "git status"})
```

## Safe Commands

### File Operations
- `ls`, `cat`, `head`, `tail`, `find`, `stat`

### System Info
- `ps`, `top`, `free`, `df`, `uname`, `whoami`

### Text Processing
- `grep`, `awk`, `sed`, `sort`, `wc`

### Development Tools
- `git status`, `python --version`, `npm list`

## Blocked Operations

❌ **File modifications**: `rm`, `mv`, `cp`, `chmod`
❌ **System control**: `sudo`, `kill`, `reboot`
❌ **Code execution**: `eval`, `exec`, `python -c`
❌ **Output redirection**: `>`, `>>`, `|`

## Testing

```bash
# Run tests
poetry run python test_planner_node.py

# Run examples
poetry run python planner_examples.py
```

## File Structure

```
playground/01-langgraph-basics/
├── planner_node.py           # Core implementation
├── multi_agent_system.py     # Updated system
├── test_planner_node.py      # Test suite
├── planner_examples.py       # Usage examples
├── PLANNER_GUIDE.md          # This guide
└── test_sandbox/             # Test project for examples
    ├── README.md             # Project documentation
    ├── requirements.txt      # Dependencies
    ├── config.yaml          # Configuration
    ├── src/                 # Source code
    ├── tests/               # Test files
    ├── docs/                # Documentation
    └── scripts/             # Build scripts
```

## Routing Examples

Queries that route to PLANNER:
- "Create a plan for..."
- "Analyze the system..."
- "Break down this task..."
- "What files are in..."

## Troubleshooting

### Common Issues

1. **Import Error**
   - Ensure `planner_node.py` is in the same directory

2. **Permission Denied**
   - Check file permissions and directory access

3. **Command Rejected**
   - Verify command is in the whitelist

4. **API Error**
   - Ensure `OPENROUTER_API_KEY` is set correctly

## Contributing

To extend the PLANNER node:

1. **Add new tools**: Implement `@tool` decorated functions
2. **Update whitelist**: Add safe commands to `SAFE_BASH_COMMANDS`
3. **Create prompts**: Add specialized system prompts
4. **Add tests**: Include tests for new functionality

---

# PLANNER节点指南

## 概述

PLANNER节点是一个专门的AI智能体，用于在多智能体系统中进行综合任务分析、规划和安全信息收集。

## 主要功能

### 🔧 工具
- **`read_file`**: 安全文件读取
- **`list_directory`**: 目录探索
- **`execute_safe_bash`**: 白名单命令执行

### 🔒 安全性
- **命令白名单**: 60+个安全只读命令
- **路径验证**: 防止目录遍历
- **超时保护**: 30秒限制
- **输出限制**: 防止内存溢出

## 快速开始

### 环境设置
```bash
export OPENROUTER_API_KEY="your-api-key"
```

### 基本使用
```python
from multi_agent_system import run_multi_agent_query_stream

query = "分析当前项目结构并创建改进计划"

messages = [{"type": "human", "content": query}]
for event in run_multi_agent_query_stream(messages):
    # 处理事件
    pass
```

## 测试

```bash
# 运行测试
poetry run python test_planner_node.py

# 运行示例
poetry run python planner_examples.py
```

## 故障排除

### 常见问题

1. **导入错误**
   - 确保`planner_node.py`在同一目录中

2. **权限拒绝**
   - 检查文件权限和目录访问权限

3. **命令被拒绝**
   - 验证命令在白名单中

4. **API错误**
   - 确保正确设置`OPENROUTER_API_KEY`

---

*本指南提供了使用PLANNER节点的基本信息。详细实现细节请参考源代码。*
