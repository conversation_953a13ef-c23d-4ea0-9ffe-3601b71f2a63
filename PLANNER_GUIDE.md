# PLANNER Node Guide / PLANNER节点指南

## Overview / 概述

**English**: The PLANNER node is a specialized AI agent for comprehensive task analysis, planning, and safe information gathering within the multi-agent system.

**中文**: PLANNER节点是一个专门的AI智能体，用于在多智能体系统中进行综合任务分析、规划和安全信息收集。

## Key Features / 主要功能

### 🔧 Tools / 工具
- **`read_file`**: Safe file reading / 安全文件读取
- **`list_directory`**: Directory exploration / 目录探索  
- **`execute_safe_bash`**: Whitelisted command execution / 白名单命令执行

### 🔒 Security / 安全性
- **Command whitelist** / 命令白名单: 60+ safe read-only commands / 60+个安全只读命令
- **Path validation** / 路径验证: Prevents directory traversal / 防止目录遍历
- **Timeout protection** / 超时保护: 30-second limit / 30秒限制
- **Output limiting** / 输出限制: Prevents memory overflow / 防止内存溢出

## Quick Start / 快速开始

### Environment Setup / 环境设置
```bash
export OPENROUTER_API_KEY="your-api-key"
```

### Basic Usage / 基本使用
```python
from multi_agent_system import run_multi_agent_query_stream

# English query
query = "Analyze this project structure and create an improvement plan"

# Chinese query / 中文查询
query = "分析当前项目结构并创建改进计划"

messages = [{"type": "human", "content": query}]
for event in run_multi_agent_query_stream(messages):
    # Process events / 处理事件
    pass
```

### Direct Tool Usage / 直接工具使用
```python
from planner_node import read_file, list_directory, execute_safe_bash

# Read file / 读取文件
content = read_file.invoke({"file_path": "config.py"})

# List directory / 列出目录
files = list_directory.invoke({"directory_path": "src/"})

# Execute safe command / 执行安全命令
result = execute_safe_bash.invoke({"command": "git status"})
```

## Safe Commands / 安全命令

### File Operations / 文件操作
- `ls`, `cat`, `head`, `tail`, `find`, `stat`

### System Info / 系统信息
- `ps`, `top`, `free`, `df`, `uname`, `whoami`

### Text Processing / 文本处理
- `grep`, `awk`, `sed`, `sort`, `wc`

### Development Tools / 开发工具
- `git status`, `python --version`, `npm list`

## Blocked Operations / 禁止操作

❌ **File modifications** / 文件修改: `rm`, `mv`, `cp`, `chmod`
❌ **System control** / 系统控制: `sudo`, `kill`, `reboot`
❌ **Code execution** / 代码执行: `eval`, `exec`, `python -c`
❌ **Output redirection** / 输出重定向: `>`, `>>`, `|`

## Testing / 测试

```bash
# Run tests / 运行测试
python test_planner_node.py

# Run examples / 运行示例
python planner_examples.py
```

## File Structure / 文件结构

```
playground/01-langgraph-basics/
├── planner_node.py           # Core implementation / 核心实现
├── multi_agent_system.py     # Updated system / 更新的系统
├── test_planner_node.py      # Test suite / 测试套件
├── planner_examples.py       # Usage examples / 使用示例
├── PLANNER_GUIDE.md          # This guide / 本指南
└── test_sandbox/             # Test project for examples / 示例测试项目
    ├── README.md             # Project documentation / 项目文档
    ├── requirements.txt      # Dependencies / 依赖项
    ├── config.yaml          # Configuration / 配置文件
    ├── src/                 # Source code / 源代码
    ├── tests/               # Test files / 测试文件
    ├── docs/                # Documentation / 文档
    └── scripts/             # Build scripts / 构建脚本
```

## Routing Examples / 路由示例

Queries that route to PLANNER / 路由到PLANNER的查询:
- "Create a plan for..." / "为...创建计划"
- "Analyze the system..." / "分析系统..."
- "Break down this task..." / "分解这个任务..."
- "What files are in..." / "目录中有什么文件..."

## Troubleshooting / 故障排除

### Common Issues / 常见问题

1. **Import Error** / 导入错误
   - Ensure `planner_node.py` is in the same directory
   - 确保`planner_node.py`在同一目录中

2. **Permission Denied** / 权限拒绝
   - Check file permissions and directory access
   - 检查文件权限和目录访问权限

3. **Command Rejected** / 命令被拒绝
   - Verify command is in the whitelist
   - 验证命令在白名单中

4. **API Error** / API错误
   - Ensure `OPENROUTER_API_KEY` is set correctly
   - 确保正确设置`OPENROUTER_API_KEY`

## Contributing / 贡献

To extend the PLANNER node / 扩展PLANNER节点:

1. **Add new tools** / 添加新工具: Implement `@tool` decorated functions
2. **Update whitelist** / 更新白名单: Add safe commands to `SAFE_BASH_COMMANDS`
3. **Create prompts** / 创建提示词: Add specialized system prompts
4. **Add tests** / 添加测试: Include tests for new functionality

---

*This guide provides essential information for using the PLANNER node. For detailed implementation details, refer to the source code.*

*本指南提供了使用PLANNER节点的基本信息。详细实现细节请参考源代码。*
